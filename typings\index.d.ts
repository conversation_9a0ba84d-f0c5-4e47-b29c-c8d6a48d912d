// Extend ProcessEnv to include our custom environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      APP_DEBUG: string | undefined
      NODE_ENV: 'development' | 'production' | 'test'
      PORT: string
      DATABASE_URL: string
      JWT_SECRET: string
      API_KEY: string
      WHITELIST_URLS: string | undefined
      // Add more environment variables as needed
      [key: string]: string | undefined
    }
  }
}
