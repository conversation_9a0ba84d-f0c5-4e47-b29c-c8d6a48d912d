/**
 * API Endpoints Discovery Script
 *
 * This script automatically discovers and lists all API endpoints from the codebase
 * with a visually appealing console output format.
 */

const fs = require('fs')
const path = require('path')

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bgBlue: '\x1b[44m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgRed: '\x1b[41m'
}

class EndpointDiscovery {
  constructor() {
    this.routes = []
    this.config = {
      baseUrl: 'http://localhost',
      apiVersion: 'v1',
      port: 3001
    }
    this.loadConfiguration()
  }

  /**
   * Load configuration from environment and app files
   */
  loadConfiguration() {
    try {
      // Try to load from .env file if it exists
      const envPath = path.join(process.cwd(), '.env')
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf-8')
        const portMatch = envContent.match(/PORT\s*=\s*(\d+)/)
        const apiVersionMatch = envContent.match(/API_VERSION\s*=\s*([^\s\n]+)/)

        if (portMatch) this.config.port = parseInt(portMatch[1])
        if (apiVersionMatch) this.config.apiVersion = apiVersionMatch[1]
      }

      // Load from app.ts for port fallback
      const appPath = path.join(process.cwd(), 'src', 'app.ts')
      if (fs.existsSync(appPath)) {
        const appContent = fs.readFileSync(appPath, 'utf-8')
        const portMatch = appContent.match(/process\.env\.PORT\s*\|\|\s*(\d+)/)
        if (portMatch && !process.env.PORT) {
          this.config.port = parseInt(portMatch[1])
        }
      }

      // Load API version from registerRoutes.ts
      const routesPath = path.join(
        process.cwd(),
        'src',
        'utils',
        'registerRoutes.ts'
      )
      if (fs.existsSync(routesPath)) {
        const routesContent = fs.readFileSync(routesPath, 'utf-8')
        const versionMatch = routesContent.match(
          /env\(['"`]API_VERSION['"`],\s*['"`]([^'"`]+)['"`]\)/
        )
        if (versionMatch) {
          this.config.apiVersion = versionMatch[1]
        }
      }
    } catch (_error) {
      console.warn(
        'Warning: Could not load some configuration values, using defaults'
      )
    }
  }

  /**
   * Discover all routes from the codebase
   */
  async discoverRoutes() {
    const routesDir = path.join(process.cwd(), 'src', 'routes')

    if (!fs.existsSync(routesDir)) {
      throw new Error('Routes directory not found at src/routes')
    }

    await this.scanDirectory(routesDir)
    this.sortRoutes()
  }

  /**
   * Recursively scan directory for route files
   */
  async scanDirectory(dirPath, basePath = '') {
    const items = fs.readdirSync(dirPath)

    for (const item of items) {
      const itemPath = path.join(dirPath, item)
      const stat = fs.statSync(itemPath)

      if (stat.isDirectory()) {
        await this.scanDirectory(itemPath, path.join(basePath, item))
      } else if (item.endsWith('.ts') || item.endsWith('.js')) {
        await this.parseRouteFile(itemPath, basePath)
      }
    }
  }

  /**
   * Parse a route file to extract endpoint information
   */
  parseRouteFile(filePath, basePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      const lines = content.split('\n')

      // Extract the route prefix from the directory structure
      const routePrefix = basePath ? `/${basePath}` : ''

      // Common HTTP methods to look for
      const httpMethods = [
        'get',
        'post',
        'put',
        'patch',
        'delete',
        'head',
        'options'
      ]

      lines.forEach((line, index) => {
        const trimmedLine = line.trim()

        // Look for router method calls
        for (const method of httpMethods) {
          const routerPattern = new RegExp(
            `router\\.${method}\\s*\\(\\s*['"\`]([^'"\`]+)['"\`]`,
            'i'
          )
          const match = trimmedLine.match(routerPattern)

          if (match) {
            const routePath = match[1]
            const fullPath = `/api/${this.config.apiVersion}${routePrefix}${
              routePath === '/' ? '' : routePath
            }`

            this.routes.push({
              method: method.toUpperCase(),
              path: routePath,
              fullPath,
              file: path.relative(process.cwd(), filePath),
              line: index + 1
            })
          }
        }
      })
    } catch (_error) {
      console.warn(`Warning: Could not parse file ${filePath}`)
    }
  }

  /**
   * Sort routes by method and path
   */
  sortRoutes() {
    const methodOrder = [
      'GET',
      'POST',
      'PUT',
      'PATCH',
      'DELETE',
      'HEAD',
      'OPTIONS'
    ]

    this.routes.sort((a, b) => {
      const methodComparison =
        methodOrder.indexOf(a.method) - methodOrder.indexOf(b.method)
      if (methodComparison !== 0) return methodComparison
      return a.fullPath.localeCompare(b.fullPath)
    })
  }

  /**
   * Display the discovered endpoints with beautiful formatting
   */
  displayEndpoints() {
    this.printHeader()
    this.printConfiguration()
    this.printEndpoints()
    this.printFooter()
  }

  printHeader() {
    const title = ' API ENDPOINTS DISCOVERY '
    const border = '═'.repeat(80)
    const titleBorder = ' '.repeat((80 - title.length) / 2)

    console.log(`${colors.cyan}${colors.bright}`)
    console.log(`╔${border}╗`)
    console.log(
      `║${titleBorder}${colors.bgBlue}${colors.white}${title}${colors.reset}${colors.cyan}${titleBorder} ║`
    )
    console.log(`╚${border}╝`)
    console.log(colors.reset)
  }

  printConfiguration() {
    const baseUrl = `${this.config.baseUrl}:${this.config.port}`

    console.log(
      `${colors.bright}${colors.yellow}📋 Configuration:${colors.reset}`
    )
    console.log(
      `   ${colors.dim}Base URL:${colors.reset} ${colors.green}${baseUrl}${colors.reset}`
    )
    console.log(
      `   ${colors.dim}API Version:${colors.reset} ${colors.green}${this.config.apiVersion}${colors.reset}`
    )
    console.log(
      `   ${colors.dim}Full API Base:${colors.reset} ${colors.green}${baseUrl}/api/${this.config.apiVersion}${colors.reset}`
    )
    console.log()
  }

  printEndpoints() {
    if (this.routes.length === 0) {
      console.log(`${colors.yellow}⚠️  No endpoints found${colors.reset}`)
      return
    }

    console.log(
      `${colors.bright}${colors.blue}🚀 Discovered Endpoints (${this.routes.length}):${colors.reset}`
    )
    console.log()

    // Group routes by base path for better organization
    const groupedRoutes = this.groupRoutesByBasePath()

    for (const [basePath, routes] of Object.entries(groupedRoutes)) {
      console.log(
        `${colors.bright}${colors.magenta}📁 ${basePath}${colors.reset}`
      )

      routes.forEach((route) => {
        const methodColor = this.getMethodColor(route.method)
        const methodPadded = route.method.padEnd(7)

        console.log(
          `   ${methodColor}${methodPadded}${colors.reset} ${colors.cyan}${route.fullPath}${colors.reset}`
        )
        console.log(
          `   ${colors.dim}        └─ ${route.file}:${route.line}${colors.reset}`
        )
      })
      console.log()
    }
  }

  groupRoutesByBasePath() {
    const grouped = {}

    this.routes.forEach((route) => {
      const pathParts = route.fullPath.split('/')
      const basePath = pathParts.slice(0, 4).join('/') // /api/v1/resource

      if (!grouped[basePath]) {
        grouped[basePath] = []
      }
      grouped[basePath].push(route)
    })

    return grouped
  }

  getMethodColor(method) {
    const colorMap = {
      GET: colors.green,
      POST: colors.yellow,
      PUT: colors.blue,
      PATCH: colors.magenta,
      DELETE: colors.red,
      HEAD: colors.cyan,
      OPTIONS: colors.white
    }
    return colorMap[method] || colors.white
  }

  printFooter() {
    const totalEndpoints = this.routes.length
    const uniqueMethods = [...new Set(this.routes.map((r) => r.method))].length

    console.log(`${colors.dim}${'─'.repeat(80)}${colors.reset}`)
    console.log(
      `${colors.bright}📊 Summary:${colors.reset} ${colors.green}${totalEndpoints}${colors.reset} endpoints across ${colors.green}${uniqueMethods}${colors.reset} HTTP methods`
    )
    console.log(
      `${colors.dim}Generated at: ${new Date().toLocaleString()}${colors.reset}`
    )
    console.log()
  }
}

// Main execution
async function main() {
  try {
    console.clear()

    const discovery = new EndpointDiscovery()
    await discovery.discoverRoutes()
    discovery.displayEndpoints()
  } catch (error) {
    console.error(
      `${colors.red}❌ Error discovering endpoints:${colors.reset}`,
      error
    )
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main().catch(console.error)
}

module.exports = EndpointDiscovery
