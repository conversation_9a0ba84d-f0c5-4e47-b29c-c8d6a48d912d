import mongoose from 'mongoose'
import { initializeDatabase } from '../../config/database'
import { DatabaseConnectionError, ConfigurationError } from '../../common/errors'

// Mock mongoose to control connection behavior
jest.mock('mongoose', () => ({
  connection: {
    readyState: 0,
    on: jest.fn(),
    close: jest.fn()
  },
  connect: jest.fn(),
  set: jest.fn(),
  STATES: {
    connected: 1,
    disconnected: 0
  }
}))

const mockMongoose = mongoose as jest.Mocked<typeof mongoose>

describe('Database Configuration', () => {
  const originalEnv = process.env

  beforeEach(() => {
    jest.clearAllMocks()
    process.env = { ...originalEnv }
  })

  afterEach(() => {
    process.env = originalEnv
  })

  describe('initializeDatabase', () => {
    it('should throw ConfigurationError when MONGO_URI is missing', async () => {
      delete process.env.MONGO_URI

      await expect(initializeDatabase()).rejects.toThrow(ConfigurationError)
      await expect(initializeDatabase()).rejects.toThrow(
        'Required environment variable MONGO_URI is not set'
      )
    })

    it('should throw ConfigurationError when MONGO_URI is empty', async () => {
      process.env.MONGO_URI = '   '

      await expect(initializeDatabase()).rejects.toThrow(ConfigurationError)
      await expect(initializeDatabase()).rejects.toThrow(
        'MONGO_URI cannot be empty'
      )
    })

    it('should throw ConfigurationError when MONGO_URI format is invalid', async () => {
      process.env.MONGO_URI = 'invalid-uri'

      await expect(initializeDatabase()).rejects.toThrow(ConfigurationError)
      await expect(initializeDatabase()).rejects.toThrow(
        'MONGO_URI must be a valid MongoDB connection string'
      )
    })

    it('should accept valid MongoDB URI formats', async () => {
      const validUris = [
        'mongodb://localhost:27017/test',
        'mongodb+srv://user:<EMAIL>/test'
      ]

      for (const uri of validUris) {
        process.env.MONGO_URI = uri
        mockMongoose.connection.readyState = 0
        mockMongoose.connect.mockResolvedValueOnce(undefined as any)

        await expect(initializeDatabase()).resolves.not.toThrow()
      }
    })

    it('should skip connection if already connected', async () => {
      process.env.MONGO_URI = 'mongodb://localhost:27017/test'
      mockMongoose.connection.readyState = 1 // connected

      await initializeDatabase()

      expect(mockMongoose.connect).not.toHaveBeenCalled()
    })

    it('should configure mongoose settings correctly', async () => {
      process.env.MONGO_URI = 'mongodb://localhost:27017/test'
      process.env.APP_DEBUG = 'true'
      mockMongoose.connect.mockResolvedValueOnce(undefined as any)

      await initializeDatabase()

      expect(mockMongoose.set).toHaveBeenCalledWith('debug', true)
      expect(mockMongoose.set).toHaveBeenCalledWith('strictQuery', true)
    })

    it('should use production-ready connection options', async () => {
      process.env.MONGO_URI = 'mongodb://localhost:27017/test'
      mockMongoose.connect.mockResolvedValueOnce(undefined as any)

      await initializeDatabase()

      expect(mockMongoose.connect).toHaveBeenCalledWith(
        'mongodb://localhost:27017/test',
        {
          maxPoolSize: 10,
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
          bufferMaxEntries: 0,
          bufferCommands: false
        }
      )
    })

    it('should throw DatabaseConnectionError when connection fails', async () => {
      process.env.MONGO_URI = 'mongodb://localhost:27017/test'
      const connectionError = new Error('Connection failed')
      mockMongoose.connect.mockRejectedValueOnce(connectionError)

      await expect(initializeDatabase()).rejects.toThrow(DatabaseConnectionError)
      await expect(initializeDatabase()).rejects.toThrow(
        'Failed to connect to MongoDB'
      )
    })

    it('should register connection event listeners', async () => {
      process.env.MONGO_URI = 'mongodb://localhost:27017/test'
      mockMongoose.connect.mockResolvedValueOnce(undefined as any)

      await initializeDatabase()

      expect(mockMongoose.connection.on).toHaveBeenCalledWith(
        'connected',
        expect.any(Function)
      )
      expect(mockMongoose.connection.on).toHaveBeenCalledWith(
        'error',
        expect.any(Function)
      )
      expect(mockMongoose.connection.on).toHaveBeenCalledWith(
        'disconnected',
        expect.any(Function)
      )
    })
  })
})
