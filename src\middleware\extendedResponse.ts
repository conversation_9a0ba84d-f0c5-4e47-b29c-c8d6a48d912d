import { type Request, type Response, type NextFunction } from 'express'
import { hasMessage, isEmptyObject } from '../utils/helpers'

/**
 * TODO: add more property for paginations
 */
export interface ResponseObject {
  success?: boolean
  message?: string
  data?: unknown
  errors?: unknown
  statusCode?: number
}

function extendedResponse(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  res.success = (
    message: string | ResponseObject | object | Array<[]>,
    statusCode: number = 200
  ): void => {
    let msg: unknown
    let data: unknown

    if (typeof message === 'string') {
      msg = message
    } else {
      data = message

      if (hasMessage(data)) {
        msg = data.message

        delete data.message
      }
    }

    const response = responseData(msg as string, data)

    res.status(statusCode).json({ success: true, ...response })
  }

  res.response = (data: object, statusCode: number = 200): void => {
    const response: ResponseObject = {}

    if (hasMessage(data)) {
      response.message = data.message as string
      delete data.message
    }

    res
      .status(statusCode)
      .json({ success: statusCode < 400, ...response, ...data })
  }

  res.error = (
    message: string | ResponseObject,
    statusCode: number = 400
  ): void => {
    let msg: unknown
    let data: unknown

    if (typeof message === 'string') {
      msg = message
    } else {
      data = message

      if (hasMessage(data)) {
        msg = data.message

        delete data.message
      }
    }

    const response = responseData(msg as string, data, true)

    res.status(statusCode).json({ success: false, ...response })
  }

  res.badRequest = (
    message: string = 'Bad Request',
    statusCode: number
  ): void => {
    res.error(message, statusCode)
  }

  res.forbidden = (
    message: string = 'Forbidden',
    statusCode: number = 403
  ): void => {
    res.error(message, statusCode)
  }

  res.notFound = (
    message: string = 'Not Found',
    statusCode: number = 404
  ): void => {
    res.error(message, statusCode)
  }

  res.alreadyExists = (
    message: string = 'Already exists',
    statusCode: number = 409
  ): void => {
    res.error(message, statusCode)
  }

  res.unauthorized = (
    message: string = 'Unauthorized Access',
    statusCode: number = 401
  ): void => {
    res.error(message, statusCode)
  }

  res.internalError = (
    message: string = 'Internal Server Error',
    statusCode: number = 500
  ): void => {
    res.error(message, statusCode)
  }

  next()
}

export default extendedResponse

function responseData(
  message: string | undefined,
  data?: unknown,
  isError?: boolean
): ResponseObject {
  const response: ResponseObject = {}

  if (data && !isEmptyObject(data)) {
    if (isError) {
      response.errors = data
    } else {
      response.data = data
    }
  }

  if (message) {
    response.message = message
  }

  return response
}
