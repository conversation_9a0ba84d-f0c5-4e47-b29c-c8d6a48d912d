import { type Request, type Response, type NextFunction } from 'express'
import { Types } from 'mongoose'
import User, { Password, Role } from '../models/User'
import {
  RequiredFieldError,
  ResourceConflictError,
  ResourceNotFoundError,
  ValidationError
} from '../common/errors'
import { transformError } from '../common/errors/errorUtils'

// Type definitions for better type safety
interface CreateUserBody {
  firstName: string
  lastName?: string
  email: string
  password: string
  roles?: string[]
}

interface UpdateUserBody {
  firstName?: string
  lastName?: string
  email?: string
  roles?: string[]
}

/**
 * Validates required fields for user creation
 * @param body - Request body containing user data
 * @throws {RequiredFieldError} When required fields are missing
 */
const validateCreateUserInput = (body: CreateUserBody): void => {
  const requiredFields: (keyof CreateUserBody)[] = [
    'firstName',
    'email',
    'password'
  ]

  for (const field of requiredFields) {
    const value = body[field]
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      throw new RequiredFieldError(field)
    }
  }
}

/**
 * Ensures default user role exists or creates it
 * @returns Promise<Types.ObjectId> The default role ID
 */
const ensureDefaultRole = async (): Promise<Types.ObjectId> => {
  let defaultRole = await Role.findOne({ name: 'user' })

  if (!defaultRole) {
    defaultRole = new Role({
      name: 'user',
      permissions: ['read']
    })
    await defaultRole.save()
  }

  return defaultRole._id as Types.ObjectId
}

/**
 * Creates a new user with proper validation and error handling
 */
export const createUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const body = req.body as CreateUserBody
    const { firstName, lastName, email, password, roles } = body

    // Validate required input fields
    validateCreateUserInput(body)

    // Check for existing user
    const existingUser = await User.findOne({ email })
    if (existingUser) {
      throw new ResourceConflictError('User with this email')
    }

    // Create user document
    const user = new User({
      firstName: firstName.trim(),
      lastName: lastName?.trim(),
      email: email.trim().toLowerCase()
    })

    // Create password document
    const userPassword = new Password({
      hash: password,
      user: user._id as Types.ObjectId
    })

    // Handle user roles
    if (!roles || roles.length === 0) {
      user.roles.push(await ensureDefaultRole())
    } else {
      // Add validated roles
      for (const roleId of roles) {
        user.roles.push(roleId)
      }
    }

    user.password = userPassword._id as Types.ObjectId

    // Save both documents
    await userPassword.save()
    await user.save()

    res.success(`User with email ${email} created successfully`, 201)
  } catch (error: unknown) {
    const typedError = transformError(error, 'Failed to create user')
    next(typedError)
  }
}
/**
 * Retrieves a single user by ID
 */
export const readUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { userID } = req.params

    if (!userID) {
      throw new ValidationError('User ID is required')
    }

    const user = await User.findById(userID).select('-password')

    if (!user) {
      throw new ResourceNotFoundError('User', userID)
    }

    res.success(user)
  } catch (error: unknown) {
    const typedError = transformError(error, 'Failed to read user')
    next(typedError)
  }
}

/**
 * Retrieves all users with pagination support
 */
export const readAllUser = async (
  _req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Use the data from middleware if available, otherwise fetch all users
    if (res.data) {
      res.success(res.data)
    } else {
      const users = await User.find().select('-password')
      res.success(users)
    }
  } catch (error: unknown) {
    const typedError = transformError(error, 'Failed to read users')
    next(typedError)
  }
}
/**
 * Validates update user input
 * @param body - Request body containing update data
 */
const validateUpdateUserInput = (
  body: UpdateUserBody & { password?: string }
): void => {
  // Prevent password updates through this endpoint for security
  if (body.password) {
    throw new ValidationError(
      'Password updates not allowed through this endpoint. Use password reset instead.'
    )
  }

  // Validate email format if provided
  if (body.email && typeof body.email !== 'string') {
    throw new ValidationError('Email must be a string')
  }
}

/**
 * Updates an existing user with proper validation and error handling
 */
export const updateUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { userID } = req.params
    const payload = req.body as UpdateUserBody & { password?: string }

    if (!userID) {
      throw new ValidationError('User ID is required')
    }

    // Validate input
    validateUpdateUserInput(payload)

    const user = await User.findById(userID).select('-password')

    if (!user) {
      throw new ResourceNotFoundError('User', userID)
    }

    // Update user with validated payload
    user.set(payload)
    await user.save()

    res.success({
      message: 'User updated successfully',
      data: user
    })
  } catch (error: unknown) {
    const typedError = transformError(error, 'Failed to update user')
    next(typedError)
  }
}
/**
 * Deletes a user and associated password record
 */
export const deleteUser = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { userID } = req.params

    if (!userID) {
      throw new ValidationError('User ID is required')
    }

    const user = await User.findById(userID)
    if (!user) {
      throw new ResourceNotFoundError('User', userID)
    }

    // Delete associated password record first
    await Password.findOneAndDelete({ user: user._id as Types.ObjectId })

    // Delete the user using findByIdAndDelete (modern approach)
    await User.findByIdAndDelete(userID)

    res.success('User deleted successfully!')
  } catch (error: unknown) {
    const typedError = transformError(error, 'Failed to delete user')
    next(typedError)
  }
}
