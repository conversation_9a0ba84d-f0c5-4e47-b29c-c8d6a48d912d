import Mongoose from 'mongoose'
import { envBoolean, requiredEnv } from '../utils/env'
import { DatabaseConnectionError, ConfigurationError } from '../common/errors'

// -----------------------------------------------
// Configuration Constants
// -----------------------------------------------

/** Default MongoDB connection options */
const DEFAULT_CONNECTION_OPTIONS = {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
  bufferCommands: false
} as const

// -----------------------------------------------
// State Management
// -----------------------------------------------

/** Track if event listeners have been registered to prevent duplicates */
let eventListenersRegistered = false

// -----------------------------------------------
// Utility Functions
// -----------------------------------------------

/**
 * Validates the MongoDB URI format
 * @param uri - The MongoDB connection URI to validate
 * @throws ConfigurationError if URI is invalid
 */
const validateMongoUri = (uri: string): void => {
  if (!uri.trim()) {
    throw new ConfigurationError('DATABASE_URI cannot be empty', 500, {
      details: { variable: 'DATABASE_URI', value: uri }
    })
  }

  // Basic MongoDB URI format validation
  const mongoUriPattern = /^mongodb(?:\+srv)?:\/\/.+/
  if (!mongoUriPattern.test(uri)) {
    throw new ConfigurationError(
      'MONGO_URI must be a valid MongoDB connection string',
      500,
      {
        details: {
          variable: 'MONGO_URI',
          value: uri,
          expectedFormat: 'mongodb://... or mongodb+srv://...'
        }
      }
    )
  }
}

/**
 * Registers MongoDB connection event listeners
 * Prevents duplicate listener registration
 */
const registerConnectionEventListeners = (): void => {
  if (eventListenersRegistered) {
    return
  }

  Mongoose.connection.on('connected', () => {
    if (process.env.NODE_ENV !== 'test') {
      console.log('[Database] Successfully connected to MongoDB')
    }
  })

  Mongoose.connection.on('error', (error: Error) => {
    console.error('[Database] Connection error:', {
      message: error.message,
      name: error.name,
      timestamp: new Date().toISOString()
    })
  })

  Mongoose.connection.on('disconnected', () => {
    console.warn('[Database] Disconnected from MongoDB')
  })

  // Handle process termination gracefully
  process.on('SIGINT', () => {
    void (async (): Promise<void> => {
      try {
        await Mongoose.connection.close()
        console.log(
          '[Database] Connection closed due to application termination'
        )
        process.exit(0)
      } catch (error) {
        console.error('[Database] Error during graceful shutdown:', error)
        process.exit(1)
      }
    })()
  })

  eventListenersRegistered = true
}

// -----------------------------------------------
// Main Database Initialization
// -----------------------------------------------

/**
 * Initializes the MongoDB database connection
 *
 * Features:
 * - Validates required environment variables
 * - Prevents duplicate connections
 * - Configures connection options for production use
 * - Registers event listeners for monitoring
 * - Provides graceful shutdown handling
 *
 * @throws ConfigurationError if MONGO_URI is invalid or missing
 * @throws DatabaseConnectionError if connection fails
 *
 * @example
 * ```typescript
 * try {
 *   await initializeDatabase();
 *   console.log('Database ready');
 * } catch (error) {
 *   console.error('Database initialization failed:', error);
 * }
 * ```
 */
export const initializeDatabase = async (): Promise<void> => {
  try {
    // Validate and get required configuration
    const mongoUri = requiredEnv('DATABASE_URI')
    validateMongoUri(mongoUri)

    const isDebug = envBoolean('APP_DEBUG', false)

    // Configure Mongoose settings
    Mongoose.set('debug', isDebug)
    Mongoose.set('strictQuery', true)

    // Check if already connected
    if (Mongoose.connection.readyState === Mongoose.STATES.connected) {
      if (process.env.NODE_ENV !== 'test') {
        console.log('[Database] Already connected to MongoDB')
      }
      return
    }

    // Register event listeners (only once)
    registerConnectionEventListeners()

    // Establish connection with production-ready options
    await Mongoose.connect(mongoUri, DEFAULT_CONNECTION_OPTIONS)
  } catch (error: unknown) {
    // Enhanced error logging for debugging
    if (error instanceof Error) {
      console.error('[Database] Connection failed:', {
        message: error.message,
        name: error.name,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
        timestamp: new Date().toISOString()
      })
    }

    // Re-throw configuration errors as-is
    if (error instanceof ConfigurationError) {
      throw error
    }

    // Transform other errors to DatabaseConnectionError
    throw new DatabaseConnectionError('Failed to connect to MongoDB', {
      cause: error,
      details: {
        connectionState: Mongoose.connection.readyState,
        timestamp: new Date().toISOString()
      }
    })
  }
}
